<?php

namespace Imponeer\Smarty\Extensions\Image;

use Psr\Cache\CacheItemPoolInterface;

/**
 * Smarty v5 Extension for image processing functions
 *
 * @package Imponeer\Smarty\Extensions\Image
 */
class SmartyImageExtension extends \Smarty\Extension\Base
{
    /**
     * @var CacheItemPoolInterface
     */
    private $cache;

    /**
     * SmartyImageExtension constructor.
     *
     * @param CacheItemPoolInterface $cache
     */
    public function __construct(CacheItemPoolInterface $cache)
    {
        $this->cache = $cache;
    }

    /**
     * @inheritDoc
     */
    public function getFunctionCallback(string $functionName)
    {
        switch ($functionName) {
            case 'resized_image':
                $resizeFunction = new ResizeImageFunction($this->cache);
                return [$resizeFunction, 'execute'];
        }

        return null;
    }
}
