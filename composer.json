{"name": "imponeer/smarty-image", "description": "Smarty plugin that adds some image related template syntax enchaments", "type": "library", "require": {"php": "^8.3", "imponeer/smarty-extensions-contracts": "^3.0", "intervention/image": "^2.5", "psr/cache": "^1.0|^2.0|^3.0", "ext-json": "*"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Smarty\\Extensions\\Image\\": "src/"}}, "keywords": ["smarty", "image-processing", "image", "smarty-plugins"], "require-dev": {"phpunit/phpunit": "^12.0", "symfony/cache": "^5.0|^6.0", "bentools/cartesian-product": "^1.3", "symfony/polyfill-php80": "^1.27", "symfony/dom-crawler": "^5.0|^6.0", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^2.0"}, "scripts": {"test": "phpunit --testdox --stop-on-error --stop-on-failure", "phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan analyse"}}